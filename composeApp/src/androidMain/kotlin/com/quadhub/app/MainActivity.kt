package com.quadhub.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.quadhub.app.di.RootAppBuilder
import com.quadhub.core.file.FilePicker
import com.quadhub.core.permissions.PermissionsManager

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val appContext = AppContext(applicationContext)
        val app = RootAppBuilder.build(appContext, this,"https://api.sampleapis.com/")

        enableEdgeToEdge()

        setContent {
            CoreComposeApp(
                root = app.first,
                mainViewModelProvider = { app.second }
            )
        }

        // Initialize the permission manager
        // TODO - Check for memory leaks
        PermissionsManager.initialize(this)

        // Initialize the file picker
        FilePicker.initialize(this)
    }
}

