package com.quadhub.app.di

import com.quadhub.app.AppContext
import com.quadhub.app.buildErrorLogger
import com.quadhub.app.buildFileConverter
import com.quadhub.app.buildSharedStorage
import com.quadhub.app.ui.MainViewModel
import com.quadhub.app.ui.di.MainUiComponent
import com.quadhub.app.ui.di.create
import com.quadhub.core.auth.OauthTokenProvider
import com.quadhub.core.biometrics.buildQuadhubBiometrics
import com.quadhub.core.presentation.navigation.compose.NavigatorHost
import com.quadhub.domain.auth.data.di.AuthDomainComponent
import com.quadhub.feature.auth.ui.di.AuthModule
import com.quadhub.feature.auth.ui.di.create


internal object RootAppBuilder {

    fun build(
        appContext: AppContext,
        activityContext: Any,
        serverUrl: String
    ): Pair<NavigatorHost.Root, MainViewModel> {

        val authTokenStorage = appContext.buildSharedStorage("elephant")
        val biometricsManager =
            buildQuadhubBiometrics(activityContext, appContext.buildSharedStorage("biometrics"))


        val tokenProvider = OauthTokenProvider.create(authTokenStorage)
        val networkClient =
            NetworkComponent::class.create(tokenProvider = tokenProvider, baseUrl = serverUrl)


        val errorLogger = appContext.buildErrorLogger()

        val fileConverter = appContext.buildFileConverter()
        val authModule = AuthModule::class.create()

        val authDomainComponent = AuthDomainComponent::class.create(
            client = networkClient.client.httpClient,
            errorLogger = errorLogger,
            tokenProvider = tokenProvider,
            biometricsManager = biometricsManager,
            dbInitializer = DatabaseInitializationListenerImpl(
                appContext = appContext,
                dbNameProvider = {
                    requireNotNull(tokenProvider.getEmail()) { "Email does not exists" }
                }
            )
        )

        val mainViewModel = MainUiComponent::class.create()

        val appComponent =
            ComposeAppComponent::class.create(authModule = authModule)

        return appComponent.root to mainViewModel.viewModel

    }

}