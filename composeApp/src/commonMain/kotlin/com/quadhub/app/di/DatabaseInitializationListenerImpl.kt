package com.quadhub.app.di

import com.anathem.app.AppContext
import com.anathem.app.getContext
import com.quadhub.domain.auth.data.datasource.local.DatabaseInitializationListener
import me.tatarka.inject.annotations.Inject

internal class DatabaseInitializationListenerImpl @Inject constructor(
    private val appContext: AppContext,
    private val dbNameProvider: () -> String,
) : DatabaseInitializationListener {

    override fun open(password: String) {
        QaudhubDatabaseManager.initialize(
            appContext = appContext.getContext(),
            "${dbNameProvider.invoke()}.db",
            password = password.encodeToByteArray()
        )
    }
}