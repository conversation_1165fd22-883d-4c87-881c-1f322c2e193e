[{"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.runtime-runtime-1.8.1-commonMain-jWWnJg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-1.8.1-commonMain-zZs6kQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material:material:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-1.8.1-commonMain-u9hUSQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material3-material3-1.8.1-commonMain-IWEVXQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-1.8.1-commonMain-EpcjWg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-resources-1.8.1-blockingMain-Wrum6g.klib", "sourceSetName": "<PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-resources-1.8.1-commonMain-Wrum6g.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.components:components-ui-tooling-preview:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-ui-tooling-preview-1.8.1-commonMain-LZ3_kQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.androidx.lifecycle-lifecycle-runtime-compose-2.9.0-commonMain-x0dbzQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-compose:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-compose-3.0.4-commonMain-MF5mEw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material:material-navigation:1.7.0-beta02", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-navigation-1.7.0-beta02-commonMain-j_Z1Ow.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlin:kotlin-stdlib:2.1.21", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlin-kotlin-stdlib-2.1.21-commonMain-uBNgdg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-network-ktor3:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-network-ktor3-3.0.4-commonMain-PuhmSg.klib", "sourceSetName": "commonMain"}, {"moduleId": "project ::core:datetime", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/core/datetime/build/classes/kotlin/metadata/commonMain", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.9.0-concurrentMain-ENDfhw.klib", "sourceSetName": "<PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.9.0-commonMain-ENDfhw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-1.8.1-commonMain-hDRn4A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-core-1.8.1-commonMain-2smSyw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material:material-ripple:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-ripple-1.8.1-commonMain-fA0Fvg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-text-1.8.1-commonMain-OvwrHg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.8.1-commonMain-4mJ79Q.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-common:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.androidx.lifecycle-lifecycle-common-2.9.0-commonMain-VFJLJQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime-saveable:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.runtime-runtime-saveable-1.8.1-commonMain-HY_2Hg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-geometry:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-geometry-1.8.1-commonMain-1jjAnQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-unit-1.8.1-commonMain-YP1muA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-util:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-util-1.8.1-commonMain-TL6JCg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-runtime:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.androidx.lifecycle-lifecycle-runtime-2.9.0-commonMain-EAV9lg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.annotation-internal:annotation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.annotation-internal-annotation-1.8.1-commonMain-nX1O7g.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-3.0.4-commonMain-vgEXBw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-compose-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-compose-core-3.0.4-commonMain-LVMImg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-core-3.0.4-nonJsCommonMain-9CrlFg.klib", "sourceSetName": "nonJsCommonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-core-3.0.4-commonMain-9CrlFg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-network-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.coil-kt.coil3-coil-network-core-3.0.4-commonMain-Eyr7Rw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-client-core:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-client-core-3.0.3-commonMain-HFCYJQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.6.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-datetime-0.6.1-commonMain-ilTmRA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-layout-1.8.1-commonMain-Urf8fw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module androidx.annotation:annotation:1.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/androidx.annotation-annotation-1.9.1-commonMain-WmoUwA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/com.squareup.okio-okio-3.9.1-systemFileSystemMain-ha3VpQ.klib", "sourceSetName": "systemFileSystemMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/com.squareup.okio-okio-3.9.1-zlibMain-ha3VpQ.klib", "sourceSetName": "zlibMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/com.squareup.okio-okio-3.9.1-commonMain-ha3VpQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-http:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-http-3.0.3-commonMain-GYREtg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-events:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-events-3.0.3-commonMain-4up2hg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-websocket-serialization:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-websocket-serialization-3.0.3-commonMain-j0DyRQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-sse:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-sse-3.0.3-commonMain-DCUoeA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-utils:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-utils-3.0.3-commonMain-KyHBqw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-serialization-core:1.7.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-serialization-core-1.7.3-commonMain-s2qClw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-serialization:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-serialization-3.0.3-commonMain-WTPxdA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-io:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-io-3.0.3-commonMain-JwCZCA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-websockets:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-websockets-3.0.3-commonMain-g5C8Ng.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-core:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-io-core-0.5.4-commonMain-VZyyzw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-bytestring:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-io-bytestring-0.5.4-commonMain-ptgxIQ.klib", "sourceSetName": "commonMain"}]