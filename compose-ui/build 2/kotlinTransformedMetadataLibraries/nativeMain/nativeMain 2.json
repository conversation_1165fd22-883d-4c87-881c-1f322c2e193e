[{"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.runtime-runtime-1.8.1-darwinMain-4s9igA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.runtime-runtime-1.8.1-posixMain-jWWnJg.klib", "sourceSetName": "posixMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.runtime-runtime-1.8.1-nativeMain-jWWnJg.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.runtime-runtime-1.8.1-nonJvmMain-jWWnJg.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.runtime-runtime-1.8.1-nonAndroidMain-jWWnJg.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-1.8.1-uikitMain-cCXCAA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-1.8.1-darwinMain-cCXCAA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-1.8.1-nativeMain-cCXCAA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-1.8.1-jsNativeMain-zZs6kQ.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-1.8.1-skikoMain-zZs6kQ.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.material:material:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material-material-1.8.1-nativeMain-O6Sz-Q.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.material:material:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material-material-1.8.1-jsNativeMain-u9hUSQ.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.material:material:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material-material-1.8.1-skikoMain-u9hUSQ.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material3-material3-1.8.1-darwinMain-TWqvWA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material3-material3-1.8.1-nativeMain-TWqvWA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material3-material3-1.8.1-jsNativeMain-IWEVXQ.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material3-material3-1.8.1-skikoMain-IWEVXQ.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material3-material3-1.8.1-nonJvmMain-IWEVXQ.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-1.8.1-uikitMain-13TQLA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-1.8.1-darwinMain-13TQLA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-1.8.1-nativeMain-13TQLA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-1.8.1-jsNativeMain-EpcjWg.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-1.8.1-skikoMain-EpcjWg.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.components-components-resources-1.8.1-iosMain-xPaThg.klib", "sourceSetName": "iosMain"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.components-components-resources-1.8.1-nativeMain-xPaThg.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.components-components-resources-1.8.1-skikoMain-Wrum6g.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-runtime-compose-2.9.0-nonAndroidMain-x0dbzQ.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module org.jetbrains.compose.material:material-navigation:1.7.0-beta02", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material-material-navigation-1.7.0-beta02-jbMain-j_Z1Ow.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module io.coil-kt.coil3:coil-network-ktor3:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-network-ktor3-3.0.4-nativeMain-R45fzA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module io.coil-kt.coil3:coil-network-ktor3:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-network-ktor3-3.0.4-nonJvmCommonMain-PuhmSg.klib", "sourceSetName": "nonJvmCommonMain"}, {"moduleId": "project ::core:datetime", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/core/datetime/build/classes/kotlin/metadata/iosMain/klib/datetime_iosMain", "sourceSetName": "iosMain"}, {"moduleId": "project ::core:datetime", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/core/datetime/build/classes/kotlin/metadata/appleMain/klib/datetime_appleMain", "sourceSetName": "appleMain"}, {"moduleId": "project ::core:datetime", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/core/datetime/build/classes/kotlin/metadata/nativeMain/klib/datetime_nativeMain", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.collection-internal:collection:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.collection-internal-collection-1.8.1-commonMain-5vjmwQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:atomicfu:0.23.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-atomicfu-0.23.2-nativeMain-yBS35w.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.kotlinx:atomicfu:0.23.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-atomicfu-0.23.2-commonMain-yBS35w.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.9.0-nativeDarwinMain-a-dA-A.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.9.0-nativeMain-ENDfhw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.animation-animation-1.8.1-nativeMain-TDYJhA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.animation-animation-1.8.1-jsNativeMain-hDRn4A.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-layout-1.8.1-uikitMain-gzFjrQ.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-layout-1.8.1-skikoMain-Urf8fw.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.foundation-foundation-layout-1.8.1-jsNativeMain-Urf8fw.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-text-1.8.1-uikitMain-j1TTYA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-text-1.8.1-darwinMain-j1TTYA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-text-1.8.1-nativeMain-j1TTYA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-text-1.8.1-jsNativeMain-OvwrHg.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-text-1.8.1-skikoMain-OvwrHg.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-uikit:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-uikit-1.8.1-uikitMain-oOVhOA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-util:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-util-1.8.1-uikitMain-WZy3gA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-util:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-util-1.8.1-nonJvmMain-TL6JCg.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-iosMain-SvRtdQ.klib", "sourceSetName": "iosMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-uikitMain-SvRtdQ.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-darwinMain-SvRtdQ.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-nativeMain-abF9Iw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-nativeJsMain-abF9Iw.klib", "sourceSetName": "nativeJsMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.9.4.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.skiko-skiko-0.9.4.2-commonMain-abF9Iw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.animation-animation-core-1.8.1-jsNativeMain-2smSyw.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.animation-animation-core-1.8.1-jbMain-2smSyw.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.material:material-ripple:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.material-material-ripple-1.8.1-jbMain-fA0Fvg.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-backhandler:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-backhandler-1.8.1-jbMain-aQ3Lcg.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-backhandler:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-backhandler-1.8.1-commonMain-aQ3Lcg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-graphics-1.8.1-nativeMain-s5N8KQ.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-graphics-1.8.1-skikoExcludingWebMain-4mJ79Q.klib", "sourceSetName": "skikoExcludingWebMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-graphics-1.8.1-jsNativeMain-4mJ79Q.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-graphics-1.8.1-skikoMain-4mJ79Q.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.6.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-datetime-0.6.1-darwinMain-O4UcJA.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.6.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-datetime-0.6.1-tzdbOnFilesystemMain-ilTmRA.klib", "sourceSetName": "tzdbOnFilesystemMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.6.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-datetime-0.6.1-tzfileMain-ilTmRA.klib", "sourceSetName": "tzfileMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.6.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-datetime-0.6.1-nativeMain-ilTmRA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-common:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-common-2.9.0-nonJvmMain-VFJLJQ.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-runtime:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-runtime-2.9.0-nativeMain-EAV9lg.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-runtime:2.9.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-runtime-2.9.0-nonJvmMain-EAV9lg.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel:2.8.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-2.8.4-nativeMain-I44vqw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel:2.8.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-2.8.4-nonJvmMain-I44vqw.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel:2.8.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-2.8.4-commonMain-I44vqw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-unit-1.8.1-jsNativeMain-YP1muA.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.8.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.compose.ui-ui-unit-1.8.1-jbMain-YP1muA.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module io.coil-kt.coil3:coil:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-3.0.4-nonAndroidMain-vgEXBw.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module io.coil-kt.coil3:coil-compose-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-compose-core-3.0.4-nonAndroidMain-LVMImg.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-compose:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-compose-2.8.0-alpha10-nativeMain-SWLWRA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-compose:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-compose-2.8.0-alpha10-jbMain-x_0rGg.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-compose:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-compose-2.8.0-alpha10-commonMain-x_0rGg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-core-3.0.4-nativeMain-w6b6-g.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module io.coil-kt.coil3:coil-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-core-3.0.4-nonJvmCommonMain-9CrlFg.klib", "sourceSetName": "nonJvmCommonMain"}, {"moduleId": "module io.coil-kt.coil3:coil-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-core-3.0.4-nonAndroidMain-9CrlFg.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module io.coil-kt.coil3:coil-network-core:3.0.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.coil-kt.coil3-coil-network-core-3.0.4-nonAndroidMain-Eyr7Rw.klib", "sourceSetName": "nonAndroidMain"}, {"moduleId": "module io.ktor:ktor-client-core:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-client-core-3.0.3-posixMain-HFCYJQ.klib", "sourceSetName": "posixMain"}, {"moduleId": "module androidx.annotation:annotation:1.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.annotation-annotation-1.9.1-nonJvmMain-WmoUwA.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module androidx.collection:collection:1.5.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.collection-collection-1.5.0-darwinMain-1oCDtg.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module androidx.collection:collection:1.5.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.collection-collection-1.5.0-unixMain-j7f1lg.klib", "sourceSetName": "unixMain"}, {"moduleId": "module androidx.collection:collection:1.5.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.collection-collection-1.5.0-nativeMain-j7f1lg.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module androidx.collection:collection:1.5.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.collection-collection-1.5.0-nonJvmMain-j7f1lg.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module androidx.collection:collection:1.5.0", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/androidx.collection-collection-1.5.0-commonMain-j7f1lg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-serialization-core:1.7.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-serialization-core-1.7.3-nativeMain-s2qClw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.core:core-bundle:1.0.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.core-core-bundle-1.0.1-nativeMain-fXRodQ.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.core:core-bundle:1.0.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.core-core-bundle-1.0.1-jbMain-fXRodQ.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.core:core-bundle:1.0.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.core-core-bundle-1.0.1-commonMain-fXRodQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-compose:2.8.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-compose-2.8.2-nativeMain-BUGNNA.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-compose:2.8.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-compose-2.8.2-jbMain-xCtrmg.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-compose:2.8.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-compose-2.8.2-commonMain-xCtrmg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-savedstate-2.8.2-jbMain-nn5erw.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.lifecycle-lifecycle-viewmodel-savedstate-2.8.2-commonMain-nn5erw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-common:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-common-2.8.0-alpha10-darwinMain-QHo9ow.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-common:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-common-2.8.0-alpha10-nativeMain-Lmwwsg.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-common:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-common-2.8.0-alpha10-jbMain-Lmwwsg.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-common:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-common-2.8.0-alpha10-commonMain-Lmwwsg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-runtime:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-runtime-2.8.0-alpha10-nonJvmMain-CWmBjA.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-runtime:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-runtime-2.8.0-alpha10-jbMain-CWmBjA.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.androidx.navigation:navigation-runtime:2.8.0-alpha10", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.navigation-navigation-runtime-2.8.0-alpha10-commonMain-CWmBjA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.androidx.savedstate:savedstate:1.2.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.savedstate-savedstate-1.2.2-nonJvmMain-rDkiiA.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.androidx.savedstate:savedstate:1.2.2", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.androidx.savedstate-savedstate-1.2.2-commonMain-rDkiiA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/com.squareup.okio-okio-3.9.1-appleMain-RGnYqA.klib", "sourceSetName": "appleMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/com.squareup.okio-okio-3.9.1-unixMain-ha3VpQ.klib", "sourceSetName": "unixMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/com.squareup.okio-okio-3.9.1-nativeMain-ha3VpQ.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/com.squareup.okio-okio-3.9.1-nonJvmMain-ha3VpQ.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module com.squareup.okio:okio:3.9.1", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/com.squareup.okio-okio-3.9.1-hashFunctions-ha3VpQ.klib", "sourceSetName": "hashFunctions"}, {"moduleId": "module io.ktor:ktor-http:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-http-3.0.3-posixMain-GYREtg.klib", "sourceSetName": "posixMain"}, {"moduleId": "module io.ktor:ktor-utils:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-utils-3.0.3-darwinMain-kGNXpQ.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module io.ktor:ktor-utils:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-utils-3.0.3-nixMain-KyHBqw.klib", "sourceSetName": "nix<PERSON><PERSON>"}, {"moduleId": "module io.ktor:ktor-utils:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-utils-3.0.3-posixMain-KyHBqw.klib", "sourceSetName": "posixMain"}, {"moduleId": "module io.ktor:ktor-io:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-io-3.0.3-darwinMain-G8iuZg.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module io.ktor:ktor-io:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-io-3.0.3-posixMain-JwCZCA.klib", "sourceSetName": "posixMain"}, {"moduleId": "module io.ktor:ktor-websockets:3.0.3", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/io.ktor-ktor-websockets-3.0.3-posixMain-g5C8Ng.klib", "sourceSetName": "posixMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-core:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-io-core-0.5.4-appleMain-BOEJjg.klib", "sourceSetName": "appleMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-core:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-io-core-0.5.4-nativeNonAndroidMain-VZyyzw.klib", "sourceSetName": "nativeNonAndroidMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-core:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-io-core-0.5.4-nativeMain-VZyyzw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-bytestring:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-io-bytestring-0.5.4-appleMain-Pv9upw.klib", "sourceSetName": "appleMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-io-bytestring:0.5.4", "file": "/Users/<USER>/Desktop/Desktop - Abiodun’s MacBook Air/Compose/QuadHubKMP/compose-ui/build/kotlinTransformedMetadataLibraries/nativeMain/org.jetbrains.kotlinx-kotlinx-io-bytestring-0.5.4-nativeMain-ptgxIQ.klib", "sourceSetName": "nativeMain"}]