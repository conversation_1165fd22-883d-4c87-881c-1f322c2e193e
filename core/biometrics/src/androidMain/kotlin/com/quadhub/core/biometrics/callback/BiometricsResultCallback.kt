package com.quadhub.core.biometrics.callback

import androidx.biometric.BiometricPrompt
import com.quadhub.core.biometrics.model.BiometricsErrorResult
import com.quadhub.core.biometrics.model.BiometricsResult

internal class BiometricsResultCallback(
    private val passPhraseGenerator: (BiometricPrompt.AuthenticationResult) -> String,
    private val callback: (BiometricsResult) -> Unit
) : BiometricPrompt.AuthenticationCallback() {

    override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
        super.onAuthenticationSucceeded(result)
        val encryptedInfo = passPhraseGenerator.invoke(result)
        callback.invoke(BiometricsResult.Success(secretKey = encryptedInfo))
    }

    override fun onAuthenticationFailed() {
        super.onAuthenticationFailed()
        callback.invoke(BiometricsResult.Failed)
    }

    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
        super.onAuthenticationError(errorCode, errString)
        callback.invoke(BiometricsResult.Failed(type = BiometricsErrorResult.ERROR_NONE_ENROLLED))
    }
}

