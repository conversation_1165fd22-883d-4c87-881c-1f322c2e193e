package com.quadhub.core.biometrics

import android.security.keystore.KeyProperties
import androidx.biometric.BiometricPrompt
import com.quadhub.core.biometrics.crypto.CipherManager
import com.quadhub.core.biometrics.crypto.ICipherManager
import com.quadhub.core.biometrics.sharedpref.IEncryptedIVAndTextStorage
import me.tatarka.inject.annotations.Inject


internal interface IBiometricEncryptionManager {
    fun encryptPassphrase(result: BiometricPrompt.AuthenticationResult, plainText: String): String
    fun decryptPassphrase(result: BiometricPrompt.AuthenticationResult): String
    fun hasEncryptedData(): <PERSON><PERSON>an
}

internal class BiometricEncryptionManager @Inject constructor(
    private val sharedPref: IEncryptedIVAndTextStorage
) : IBiometricEncryptionManager,
    ICipherManager by CipherManager(encryptedIvAndByteArrayProvider = { sharedPref.get() }) {

    override fun encryptPassphrase(result: BiometricPrompt.AuthenticationResult, plainText: String): String {
        val encryptionCipher =
            result.cryptoObject?.cipher ?: throw IllegalStateException("Cipher is null")
        val encryptedBytes = encryptionCipher.doFinal(plainText.toByteArray(Charsets.UTF_8))
        val encryptedIvAndText = encryptionCipher.iv + encryptedBytes
        return sharedPref.set(encryptedIvAndText)
    }

    override fun hasEncryptedData(): Boolean {
        return sharedPref.hasEncryptedData()
    }

    override fun decryptPassphrase(result: BiometricPrompt.AuthenticationResult): String {
        val encryptedIvAndText = sharedPref.get()

        val decryptionCipher =
            result.cryptoObject?.cipher ?: throw IllegalStateException("Cipher is null")

        // Extract the actual encrypted text (removing IV)
        val encryptedBytes = encryptedIvAndText.copyOfRange(12, encryptedIvAndText.size)

        val decryptedBytes = decryptionCipher.doFinal(encryptedBytes)
        val decryptedText = String(decryptedBytes, Charsets.UTF_8)
        return decryptedText
    }

    companion object {
        const val ENCRYPTION_BLOCK_MODE = KeyProperties.BLOCK_MODE_GCM
        const val ENCRYPTION_PADDING = KeyProperties.ENCRYPTION_PADDING_NONE
        const val ENCRYPTION_ALGORITHM = KeyProperties.KEY_ALGORITHM_AES

        private const val AUTH_TIMEOUT_SECONDS = 30
    }
}
