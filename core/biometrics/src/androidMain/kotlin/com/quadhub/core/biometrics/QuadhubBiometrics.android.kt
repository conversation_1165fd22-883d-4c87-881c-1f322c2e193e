package com.quadhub.core.biometrics

import androidx.fragment.app.FragmentActivity
import com.quadhub.core.biometrics.sharedpref.EncryptedIVAndTextStorage
import com.quadhub.core.sharedpref.AppSharedStorageProvider


actual fun buildQuadhubBiometrics(
    context: Any,
    appSharedStorageProvider: AppSharedStorageProvider
): BiometricsRequestManager {
    return CoreBiometricsRequestManager(
        activity = context as FragmentActivity,
        encryptionManager = BiometricEncryptionManager(
            sharedPref = EncryptedIVAndTextStorage(
                sharedPref = appSharedStorageProvider
            )
        ),
    )
}
