package com.quadhub.core.biometrics.model

sealed class BiometricsResult {
    data class Success(val secretKey: String) : BiometricsResult()
    data class Failed(val type: BiometricsErrorResult? = null) : BiometricsResult()

    companion object {
        val Failed = Failed(null)
    }
}

enum class BiometricsEnrollmentStatus {
    ENROLLED, NOT_ENROLLED
}

enum class BiometricsErrorResult {
    ERROR_NO_HARDWARE, ERROR_HW_UNAVAILABLE, ERROR_NONE_ENROLLED
}