package com.quadhub.core.biometrics

import com.quadhub.core.biometrics.model.BiometricsEnrollmentStatus
import com.quadhub.core.biometrics.model.BiometricsResult
import com.quadhub.core.sharedpref.AppSharedStorageProvider
import kotlinx.coroutines.flow.Flow

interface BiometricsRequestManager {
    /**
     * @param passphrase - this is the passphrase that is to be encrypted.
     * NB: This should only be generated once. Subsequent request should be
     */
    fun request(passphrase: String?): Flow<BiometricsResult>
    suspend fun canAuthenticateWithBiometrics(): Boolean
    fun enrollmentStatus(): BiometricsEnrollmentStatus
}

expect fun buildQuadhubBiometrics(context: Any, appSharedStorageProvider: AppSharedStorageProvider): BiometricsRequestManager
