CREATE TABLE ProfileEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    officer_id TEXT NOT NULL,
    created_time TEXT NOT NULL,
    user_id TEXT,
    email TEXT,
    badge_number TEXT,
    first_name TEXT,
    last_name TEXT,
    job_role TEXT,
    organization TEXT,
    rank TEXT,
    department TEXT,
    hire_date TEXT,
    status TEXT
);

fetchAll:
SELECT * FROM ProfileEntity;

insert:
INSERT OR REPLACE INTO ProfileEntity(_id, officer_id, created_time, user_id, email, badge_number, first_name, last_name, job_role, organization, rank, department, hire_date, status)
VALUES ?;

fetchById:
SELECT * FROM ProfileEntity WHERE _id = :id;

updateOfficerId:
UPDATE ProfileEntity SET officer_id = :officerId WHERE _id = :id;

deleteAll:
DELETE FROM ProfileEntity;

