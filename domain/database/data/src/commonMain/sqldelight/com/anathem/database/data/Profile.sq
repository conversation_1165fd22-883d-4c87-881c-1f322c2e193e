CREATE TABLE ProfileEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    created_time TEXT NOT NULL,
    user_id TEXT,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    university TEXT
);

fetchAll:
SELECT * FROM ProfileEntity;

insert:
INSERT OR REPLACE INTO ProfileEntity(_id, created_time, user_id, email, first_name, last_name, university)
VALUES ?;

fetchById:
SELECT * FROM ProfileEntity WHERE _id = :id;

updateUserId:
UPDATE ProfileEntity SET user_id = :userId WHERE _id = :id;

deleteAll:
DELETE FROM ProfileEntity;

