CREATE TABLE InteractionEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    created_time TEXT,
    officer_id TEXT,
    schedule_time TEXT,
    interaction_location TEXT,
    completion_time TEXT,
    interaction_type TEXT,
    interaction_category TEXT,
    incident_ref TEXT,
    offences_list TEXT,
    case_id TEXT,
    status TEXT,
    officer_notes TEXT,
    interviewee_person TEXT,
    FOREIGN KEY (interviewee_person) REFERENCES InteractionPersonEntity(_id)
);

CREATE TABLE InteractionPersonEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    first_name TEXT,
    last_name TEXT,
    legal_role TEXT
);

fetchAll:
SELECT ie.*, ipe.* FROM InteractionEntity ie
LEFT JOIN InteractionPersonEntity ipe ON ie.interviewee_person == ipe._id;

searchAll:
SELECT * FROM InteractionEntity WHERE status LIKE :queryText OR _id LIKE :queryText OR interaction_category LIKE :queryText;

insert:
INSERT OR REPLACE INTO InteractionEntity(_id, created_time, officer_id, schedule_time, interaction_location, completion_time, interaction_type, interaction_category, incident_ref, offences_list, case_id, status, officer_notes, interviewee_person)
VALUES ?;

insertPerson:
INSERT OR REPLACE INTO InteractionPersonEntity(_id, first_name, last_name, legal_role)
VALUES ?;

fetchById:
SELECT ie.*, ipe.* FROM InteractionEntity ie
LEFT JOIN InteractionPersonEntity ipe ON ie.interviewee_person == ipe._id
WHERE ie._id ==:id;

deleteAll:
DELETE FROM InteractionEntity;

deleteById:
DELETE FROM InteractionEntity WHERE _id == :id;