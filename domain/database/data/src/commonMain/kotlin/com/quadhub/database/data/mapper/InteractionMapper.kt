package com.quadhub.database.data.mapper

import com.quadhub.database.dto.LocalInteraction
import com.quadhub.database.dto.InteractionUser
import me.tatarka.inject.annotations.Inject

internal interface InteractionMapper {
    fun mapTo(entity: InteractionEntity): LocalInteraction
    fun mapFrom(dto: LocalInteraction): InteractionEntity
    fun toUser(user: InteractionUser): InteractionPersonEntity
    fun fromUser(entity: InteractionPersonEntity): InteractionUser
}

internal class InteractionMapperImpl @Inject constructor() : InteractionMapper {

    override fun mapTo(entity: InteractionEntity): LocalInteraction {
        return with(entity) {
            LocalInteraction(
                id = _id,
                createdTime = created_time ?: "",
                officerId = officer_id ?: "",
                postPerson = null, // Will be populated separately with join queries
                otherPersons = null, // Will be populated separately with join queries
                status = status
            )
        }
    }

    override fun mapFrom(dto: LocalInteraction): InteractionEntity {
        return with(dto) {
            InteractionEntity(
                _id = id,
                created_time = createdTime,
                officer_id = officerId,
                status = status,
                post_person = postPerson?.id
            )
        }
    }

    override fun toUser(user: InteractionUser): InteractionPersonEntity {
        return with(user) {
            InteractionPersonEntity(
                _id = id,
                first_name = firstName,
                last_name = lastName
            )
        }
    }

    override fun fromUser(entity: InteractionPersonEntity): InteractionUser {
        return with(entity) {
            InteractionUser(
                id = _id,
                firstName = first_name ?: "",
                lastName = last_name ?: ""
            )
        }
    }
}