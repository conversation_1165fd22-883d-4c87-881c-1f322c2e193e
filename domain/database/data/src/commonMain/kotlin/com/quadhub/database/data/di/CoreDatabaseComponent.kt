package com.quadhub.database.data.di

import com.quadhub.database.DaoProvider
import com.quadhub.database.InteractionDao
import com.quadhub.database.ProfileDao
import com.quadhub.database.data.InteractionDaoImpl
import com.quadhub.database.data.ProfileDaoImpl
import com.quadhub.database.data.mapper.InteractionMapperImpl
import com.quadhub.database.data.mapper.ProfileMapperImpl
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
abstract class CoreDatabaseComponent : DaoProvider {

    @Provides
    fun provideInteractionDao(): InteractionDao = InteractionDaoImpl(
        mapper = InteractionMapperImpl()
    )

    @Provides
    fun provideProfileDao(): ProfileDao = ProfileDaoImpl(
        mapper = ProfileMapperImpl()
    )

}
