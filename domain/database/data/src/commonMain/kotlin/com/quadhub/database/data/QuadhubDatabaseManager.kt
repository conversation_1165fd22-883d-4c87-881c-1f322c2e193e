package com.quadhub.database.data

import app.cash.sqldelight.db.SqlDriver

interface QuadhubDatabaseManager {
    fun isOpen(): Boolean
    fun open(databaseName: String, password: ByteArray): SqlDriver
    fun destroy(databaseName: String? = null)

    companion object Companion {

        private var dbManager: QuadhubDatabaseManager? = null
            private set

        val driver : SqlDriver
            get() = _current

        private var initialized= false
        private lateinit var _current: SqlDriver

        fun initialize(appContext: Any, databaseName: String, password: ByteArray) {
            if (initialized) throw IllegalStateException("SQLDriver has already been initialized")
            dbManager = buildDatabaseManager(appContext = appContext)
            _current = requireNotNull(dbManager).open(databaseName, password)
        }

        /**
         * Call this when the user logs out
         */
        fun destroy() {
            this.initialized = false
            dbManager?.destroy()
        }

    }
}
