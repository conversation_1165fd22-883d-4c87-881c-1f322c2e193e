package com.quadhub.database.data.mapper


import me.tatarka.inject.annotations.Inject

internal interface ProfileMapper {
    fun map(entity: ProfileEntity): LocalUserProfile
    fun mapToEntity(dto: LocalUserProfile): ProfileEntity
}

internal class ProfileMapperImpl @Inject constructor() : ProfileMapper {

    override fun map(entity: ProfileEntity): LocalUserProfile {
        return with(entity) {
            LocalUserProfile(
                id = _id,
                createdTime = created_time,
                userId = requireNotNull(user_id),
                email = requireNotNull(email),
                badgeNumber = badge_number,
                firstName = first_name,
                lastName = last_name,
                rank = rank,
                department = department,
                hireDate = hire_date,
                status = status,
                officerId = officer_id,
                jobRole = job_role,
                organization = organization
            )
        }
    }

    override fun mapToEntity(dto: LocalUserProfile): ProfileEntity {
        return with(dto) {
            ProfileEntity(
                _id = id,
                created_time = createdTime,
                user_id = userId,
                email = email,
                badge_number = badgeNumber,
                first_name = firstName,
                last_name = lastName,
                rank = rank,
                department = department,
                hire_date = hireDate,
                status = status,
                officer_id = officerId,
                job_role = jobRole,
                organization = organization
            )
        }
    }

}