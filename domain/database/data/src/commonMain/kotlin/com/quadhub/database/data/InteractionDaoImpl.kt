package com.anathem.database.data

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import app.cash.sqldelight.db.SqlDriver
import com.anathem.database.InteractionDao
import com.anathem.database.data.mapper.InteractionMapper
import com.anathem.database.dto.LocalInteraction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class InteractionDaoImpl @Inject constructor(
    private val mapper: InteractionMapper,
) : InteractionDao {

    /**
     * <PERSON><PERSON> initialize the Queries. This will throw an exception if the Database has not been initialized.
     */
    private val interactionQueries: InteractionQueries by lazy { InteractionQueries(driver = AnathemDatabaseManager.driver) }

    override fun observe(queryText: String?): Flow<List<LocalInteraction>> {
        val query = interactionQueries.fetchAll()

        return query
            .asFlow()
            .mapToList(Dispatchers.Default)
            .map { it.map(mapper::mapTo) }
    }

    override suspend fun get(id: String): LocalInteraction {
        TODO()
//        return interactionQueries
//            .fetchById(id)
//            .executeAsOne()
//            .let(mapper::mapTo)
    }

    override suspend fun insert(interaction: LocalInteraction): Boolean {
        val entity = mapper.mapFrom(dto = interaction)
        return interactionQueries.insert(entity).let { true }
    }

    override suspend fun insertAll(interactions: List<LocalInteraction>) {
        interactionQueries.transaction {
            val persons = interactions.mapNotNull { it.intervieweePerson }
            if (persons.isNotEmpty()) {
                persons.forEach {
                    interactionQueries.insertPerson(it.let(mapper::toUser))
                }
            }
            val transactions = interactions.map(mapper::mapFrom)
            transactions.forEach {
                interactionQueries.insert(it)
            }
        }
    }

    override suspend fun delete(interactionId: String): Boolean {
        interactionQueries.deleteById(id = interactionId)
        return true
    }

    override suspend fun fetchAll(): List<LocalInteraction> {
        return interactionQueries.fetchAll().executeAsList().map(mapper::mapTo)
    }
}