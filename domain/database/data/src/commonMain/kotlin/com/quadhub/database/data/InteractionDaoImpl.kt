package com.quadhub.database.data

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import com.quadhub.database.InteractionDao
import com.quadhub.database.data.mapper.InteractionMapper
import com.quadhub.database.dto.LocalInteraction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class InteractionDaoImpl @Inject constructor(
    private val mapper: InteractionMapper,
) : InteractionDao {

    /**
     * Lazy initialize the Queries. This will throw an exception if the Database has not been initialized.
     */
    private val interactionQueries: InteractionQueries by lazy { InteractionQueries(driver = QuadhubDatabaseManager.driver) }
    override fun observe(queryText: String?): Flow<List<LocalInteraction>> {
        val query = if (queryText.isNullOrBlank()) {
            interactionQueries.fetchAll()
        } else {
            interactionQueries.searchAll(queryText)
        }

        return query
            .asFlow()
            .mapToList(Dispatchers.Default)
            .map { entities ->
                entities.map { entity ->
                    val interaction = mapper.mapTo(entity)
                    // Fetch other persons for this interaction
                    val otherPersons = interactionQueries.fetchOtherPersons(entity._id)
                        .executeAsList()
                        .map(mapper::fromUser)

                    interaction.copy(otherPersons = otherPersons)
                }
            }
    }

    override suspend fun get(id: String): LocalInteraction {
        val entity = interactionQueries.fetchById(id).executeAsOne()
        val interaction = mapper.mapTo(entity)

        // Fetch other persons for this interaction
        val otherPersons = interactionQueries.fetchOtherPersons(id)
            .executeAsList()
            .map(mapper::fromUser)

        return interaction.copy(otherPersons = otherPersons)
    }

    override suspend fun insert(interaction: LocalInteraction): Boolean {
        return try {
            val entity = mapper.mapFrom(interaction)
            interactionQueries.insert(entity)

            // Insert post person if exists
            interaction.postPerson?.let { postPerson ->
                interactionQueries.insertPerson(mapper.toUser(postPerson))
            }

            // Insert other persons if exist
            interaction.otherPersons?.forEach { otherPerson ->
                interactionQueries.insertPerson(mapper.toUser(otherPerson))
                interactionQueries.insertOtherPerson(interaction.id, otherPerson.id)
            }

            true
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun delete(interactionId: String): Boolean {
        return try {
            interactionQueries.deleteById(interactionId)
            true
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun insertAll(interactions: List<LocalInteraction>) {
        interactionQueries.transaction {
            interactions.forEach { interaction ->
                insert(interaction)
            }
        }
    }

    override suspend fun fetchAll(): List<LocalInteraction> {
        return interactionQueries.fetchAll()
            .executeAsList()
            .map { entity ->
                val interaction = mapper.mapTo(entity)
                // Fetch other persons for this interaction
                val otherPersons = interactionQueries.fetchOtherPersons(entity._id)
                    .executeAsList()
                    .map(mapper::fromUser)

                interaction.copy(otherPersons = otherPersons)
            }
    }

}