package com.quadhub.database.data

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import com.quadhub.database.InteractionDao
import com.quadhub.database.data.mapper.InteractionMapper
import com.quadhub.database.dto.LocalInteraction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class InteractionDaoImpl @Inject constructor(
    private val mapper: InteractionMapper,
) : InteractionDao {

    /**
     * <PERSON>zy initialize the Queries. This will throw an exception if the Database has not been initialized.
     */
    private val interactionQueries: InteractionQueries by lazy { InteractionQueries(driver = QuadhubDatabaseManager.driver) }
    override fun observe(queryText: String?): Flow<List<LocalInteraction>> {
        TODO("Not yet implemented")
    }


    override suspend fun get(id: String): LocalInteraction {
        TODO()
//        return interactionQueries
//            .fetchById(id)
//            .executeAsOne()
//            .let(mapper::mapTo)
    }

    override suspend fun insert(interaction: LocalInteraction): Boolean {
        TODO("Not yet implemented")
    }

    override suspend fun delete(interactionId: String): Boolean {
        TODO("Not yet implemented")
    }

    override suspend fun insertAll(interactions: List<LocalInteraction>) {
        TODO("Not yet implemented")
    }

    override suspend fun fetchAll(): List<LocalInteraction> {
        TODO("Not yet implemented")
    }

}