package com.quadhub.domain.auth.data.di

import com.quadhub.core.auth.OauthTokenProvider
import com.quadhub.core.biometrics.BiometricsRequestManager
import com.quadhub.core.log.ErrorLogger
import com.quadhub.domain.auth.AuthRepositories
import com.quadhub.domain.auth.AuthRepository
import com.quadhub.domain.auth.BiometricsRepository
import com.quadhub.domain.auth.data.AuthRepositoryImpl
import com.quadhub.domain.auth.data.BiometricsRepositoryImpl
import com.quadhub.domain.auth.data.datasource.api.AuthServiceImpl
import com.quadhub.domain.auth.data.datasource.local.DatabaseInitializationListener
import com.quadhub.domain.auth.data.datasource.remote.AuthDataSourceImpl
import com.quadhub.domain.auth.data.datasource.remote.mapper.AuthDtoMapperImpl
import io.ktor.client.HttpClient
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
abstract class AuthDomainComponent(
    private val client: HttpClient,
    private val errorLogger: ErrorLogger,
    private val tokenProvider: OauthTokenProvider,
    private val biometricsManager: BiometricsRequestManager,
    private val dbInitializer: DatabaseInitializationListener,
) : AuthRepositories {

    @Provides
    fun provideAuthRepository(): AuthRepository {
        return AuthRepositoryImpl(
            dataSource = AuthDataSourceImpl(
                service = AuthServiceImpl(httpClient = client),
                mapper = AuthDtoMapperImpl()
            ),
            errorLogger = errorLogger,
            tokenProvider = tokenProvider,
        )
    }

    @Provides
    fun provideBiometricsRepository(): BiometricsRepository = BiometricsRepositoryImpl(
        biometricsManager = biometricsManager,
        dbInitializer = dbInitializer,
        errorLogger = errorLogger
    )

}