package com.quadhub.domain.auth.data.datasource.remote

import com.quadhub.domain.auth.data.datasource.api.AuthService
import com.quadhub.domain.auth.data.datasource.api.input.LoginApiRequest
import com.quadhub.domain.auth.data.datasource.api.input.VerifyOtpApiRequest
import com.quadhub.domain.auth.data.datasource.dto.LoginUserDto
import com.quadhub.domain.auth.data.datasource.remote.mapper.AuthDtoMapper
import me.tatarka.inject.annotations.Inject

internal interface AuthDataSource {
    suspend fun verifyOtp(otp: String): <PERSON><PERSON><PERSON>
    suspend fun login(email: String, password: String): LoginUserDto
}

internal class AuthDataSourceImpl @Inject constructor(
    private val service: AuthService,
    private val mapper: AuthDtoMapper
) : AuthDataSource {

    override suspend fun verifyOtp(otp: String): Boolean {
        val response = service.verifyOtp(VerifyOtpApiRequest(otp = otp))
        return response.let { true }
    }

    override suspend fun login(email: String, password: String): LoginUserDto {
        val response = service.login(LoginApiRequest(email = email, password = password))
        return response.let(mapper::map)
    }
}
