package com.quadhub.domain.auth.data

import com.quadhub.core.biometrics.BiometricsRequestManager
import com.quadhub.core.biometrics.model.BiometricsEnrollmentStatus
import com.quadhub.core.biometrics.model.BiometricsResult
import com.quadhub.domain.auth.BiometricsAvailabilityEntityResult
import com.quadhub.domain.auth.BiometricsEnrollmentEntityResult
import com.quadhub.domain.auth.BiometricsEntityResult
import com.quadhub.domain.auth.BiometricsRepository
import com.quadhub.domain.auth.SkipBiometricsEntityResult
import com.quadhub.domain.auth.data.datasource.local.DatabaseInitializationListener
import com.quadhub.core.log.ErrorLogger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class BiometricsRepositoryImpl @Inject constructor(
    private val biometricsManager: BiometricsRequestManager,
    private val dbInitializer: DatabaseInitializationListener,
    private val errorLogger: ErrorLogger
) : BiometricsRepository {

    override fun observeBiometrics(): Flow<BiometricsEntityResult> {
        val passphrase = when (isBiometricsEnabled()) {
            true -> null
            false -> generateDatabasePassphrase()
        }

        return biometricsManager.request(passphrase = passphrase).map {
            when (it) {
                is BiometricsResult.Success -> {
                    // Use the biometrics result to initialize the database
                    val actualPassphrase = passphrase ?: it.secretKey
                    dbInitializer.open(password = actualPassphrase)
                    BiometricsEntityResult.Success
                }
                else -> BiometricsEntityResult.Failure
            }
        }
    }

    override suspend fun biometricsAvailability(): BiometricsAvailabilityEntityResult {
        return when (biometricsManager.canAuthenticateWithBiometrics()) {
            true -> BiometricsAvailabilityEntityResult.Success
            false -> BiometricsAvailabilityEntityResult.Failure
        }
    }

    override suspend fun biometricsEnrollment(openDatabase: Boolean): BiometricsEnrollmentEntityResult {
        return when (isBiometricsEnabled()) {
            true -> BiometricsEnrollmentEntityResult.Enrolled
            false -> {
                if (openDatabase) {
                    // if the user is not enrolled. Try to open the database
                    dbInitializer.open(password = DEFAULT_PASSPHRASE)
                }
                BiometricsEnrollmentEntityResult.UnEnrolled
            }
        }
    }

    override suspend fun skipBiometrics(): SkipBiometricsEntityResult {
        return try {
            dbInitializer.open(password = DEFAULT_PASSPHRASE)
            SkipBiometricsEntityResult.Success
        } catch (exception: Exception) {
            errorLogger.log(exception)
            SkipBiometricsEntityResult.Failure
        }
    }

    private fun isBiometricsEnabled(): Boolean {
        return when (biometricsManager.enrollmentStatus()) {
            BiometricsEnrollmentStatus.ENROLLED -> true
            else -> false
        }
    }

    private fun generateDatabasePassphrase(): String {
        return "passphrase"
    }

    companion object {
        private const val DEFAULT_PASSPHRASE = "passphrase"
    }

}