package com.quadhub.domain.auth.data.datasource.api

import com.quadhub.domain.auth.data.datasource.api.input.LoginApiRequest
import com.quadhub.domain.auth.data.datasource.api.input.VerifyOtpApiRequest
import com.quadhub.domain.auth.data.datasource.api.response.LoginApiResponse
import com.quadhub.domain.auth.data.datasource.api.response.VerifyOtpApiResponse
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import me.tatarka.inject.annotations.Inject


internal interface AuthService {
    suspend fun verifyOtp(body: VerifyOtpApiRequest): VerifyOtpApiResponse
    suspend fun login(body: LoginApiRequest): LoginApiResponse
}

internal class AuthServiceImpl @Inject constructor(
    private val httpClient: HttpClient
) : AuthService {

    override suspend fun verifyOtp(body: VerifyOtpApiRequest): VerifyOtpApiResponse {
        return httpClient.post("auth/verify-otp") {
            contentType(ContentType.Application.Json)
            setBody(body)
        }.body()
    }

    override suspend fun login(body: LoginApiRequest): LoginApiResponse {
        return httpClient.post("auth/login") {
            contentType(ContentType.Application.Json)
            setBody(body)
        }.body()
    }

}