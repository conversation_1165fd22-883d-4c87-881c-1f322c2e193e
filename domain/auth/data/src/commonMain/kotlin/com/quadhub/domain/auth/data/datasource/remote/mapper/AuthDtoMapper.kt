package com.quadhub.domain.auth.data.datasource.remote.mapper

import com.quadhub.domain.auth.data.datasource.api.response.LoginApiResponse
import com.quadhub.domain.auth.data.datasource.dto.LoginUserDto
import me.tatarka.inject.annotations.Inject

internal interface AuthDtoMapper {
    fun map(response: LoginApiResponse): LoginUserDto
}

internal class AuthDtoMapperImpl @Inject constructor() : AuthDtoMapper {

    override fun map(response: LoginApiResponse): LoginUserDto {
        return LoginUserDto(id = "some+id")
    }

}